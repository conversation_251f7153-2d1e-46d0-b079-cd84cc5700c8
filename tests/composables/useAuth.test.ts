import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { ref } from 'vue'

// Mock the composables before importing useAuth
vi.mock('~/composables/useApi', () => ({
  useApi: () => ({
    api: vi.fn()
  })
}))

vi.mock('~/composables/useNotification', () => ({
  useNotification: () => ({
    showError: vi.fn(),
    showSuccess: vi.fn(),
    showInfo: vi.fn(),
    showWarning: vi.fn()
  })
}))

// Mock useCookie
const mockCookie = ref(null)
global.useCookie = vi.fn(() => mockCookie)

import { useAuth } from '../../composables/useAuth'

describe('useAuth Composable', () => {
  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks()
    // Reset cookie mock
    mockCookie.value = null
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('initializes with default state', () => {
    const auth = useAuth()

    expect(auth.user.value).toBeNull()
    expect(auth.isLoggedIn.value).toBe(false)
    expect(auth.isLoading.value).toBe(false)
  })

  it('can set and clear token', () => {
    const auth = useAuth()

    // Initially no token
    expect(mockCookie.value).toBeNull()

    // Set token
    auth.setToken('test-token')
    expect(mockCookie.value).toBe('test-token')

    // Clear token
    auth.clearToken()
    expect(mockCookie.value).toBeNull()
  })

  it('tracks loading state', () => {
    const auth = useAuth()

    // Initially not loading
    expect(auth.isLoading.value).toBe(false)

    // Since isLoading is readonly, we just test the initial state
    // The loading state would be managed internally by the composable methods
  })

  it('computes isLoggedIn based on user state', () => {
    const auth = useAuth()

    // Initially not logged in
    expect(auth.isLoggedIn.value).toBe(false)

    // Since user is readonly, we test the computed property logic
    // by checking that it returns false when no user is set
    expect(auth.user.value).toBeNull()
    expect(auth.isLoggedIn.value).toBe(false)
  })
})
