import { http, HttpResponse } from 'msw'
import { createMockUser, createMockMusic, createMockPlaylist } from '../utils/test-utils'

const API_BASE = 'http://localhost:3000/api/v1'

export const handlers = [
  // Auth endpoints
  http.post(`${API_BASE}/auth/login`, async ({ request }) => {
    const { email, password } = await request.json() as any
    
    if (email === '<EMAIL>' && password === 'password123') {
      return HttpResponse.json({
        success: true,
        data: {
          user: createMockUser({ email }),
          token: 'mock-jwt-token',
          refreshToken: 'mock-refresh-token'
        }
      })
    }
    
    return HttpResponse.json(
      { success: false, error: 'Invalid credentials' },
      { status: 401 }
    )
  }),

  http.post(`${API_BASE}/auth/register`, async ({ request }) => {
    const userData = await request.json() as any
    
    return HttpResponse.json({
      success: true,
      data: {
        user: createMockUser({
          username: userData.username,
          email: userData.email
        }),
        token: 'mock-jwt-token',
        refreshToken: 'mock-refresh-token'
      }
    })
  }),

  http.post(`${API_BASE}/auth/logout`, () => {
    return HttpResponse.json({ success: true })
  }),

  http.post(`${API_BASE}/auth/refresh`, () => {
    return HttpResponse.json({
      success: true,
      data: {
        token: 'new-mock-jwt-token',
        refreshToken: 'new-mock-refresh-token'
      }
    })
  }),

  http.get(`${API_BASE}/auth/me`, () => {
    return HttpResponse.json({
      success: true,
      data: createMockUser()
    })
  }),

  http.put(`${API_BASE}/auth/profile`, async ({ request }) => {
    const updateData = await request.json() as any
    
    return HttpResponse.json({
      success: true,
      data: createMockUser(updateData)
    })
  }),

  http.post(`${API_BASE}/auth/forgot-password`, () => {
    return HttpResponse.json({
      success: true,
      message: 'Password reset email sent'
    })
  }),

  http.post(`${API_BASE}/auth/reset-password`, () => {
    return HttpResponse.json({
      success: true,
      message: 'Password reset successfully'
    })
  }),

  // Music endpoints
  http.get(`${API_BASE}/music`, ({ request }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '20')
    
    const mockMusic = Array.from({ length: limit }, (_, i) => 
      createMockMusic({ 
        id: `${(page - 1) * limit + i + 1}`,
        title: `Song ${(page - 1) * limit + i + 1}`
      })
    )
    
    return HttpResponse.json({
      success: true,
      data: {
        items: mockMusic,
        pagination: {
          page,
          limit,
          total: 100,
          totalPages: Math.ceil(100 / limit)
        }
      }
    })
  }),

  http.get(`${API_BASE}/music/:id`, ({ params }) => {
    return HttpResponse.json({
      success: true,
      data: createMockMusic({ id: params.id as string })
    })
  }),

  http.post(`${API_BASE}/music`, async ({ request }) => {
    const formData = await request.formData()
    const title = formData.get('title') as string
    
    return HttpResponse.json({
      success: true,
      data: createMockMusic({ title })
    })
  }),

  http.put(`${API_BASE}/music/:id`, async ({ params, request }) => {
    const updateData = await request.json() as any
    
    return HttpResponse.json({
      success: true,
      data: createMockMusic({ 
        id: params.id as string,
        ...updateData 
      })
    })
  }),

  http.delete(`${API_BASE}/music/:id`, () => {
    return HttpResponse.json({ success: true })
  }),

  // Playlist endpoints
  http.get(`${API_BASE}/playlists`, ({ request }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '20')
    
    const mockPlaylists = Array.from({ length: limit }, (_, i) => 
      createMockPlaylist({ 
        id: `${(page - 1) * limit + i + 1}`,
        name: `Playlist ${(page - 1) * limit + i + 1}`
      })
    )
    
    return HttpResponse.json({
      success: true,
      data: {
        items: mockPlaylists,
        pagination: {
          page,
          limit,
          total: 50,
          totalPages: Math.ceil(50 / limit)
        }
      }
    })
  }),

  http.get(`${API_BASE}/playlists/:id`, ({ params }) => {
    return HttpResponse.json({
      success: true,
      data: createMockPlaylist({ id: params.id as string })
    })
  }),

  http.post(`${API_BASE}/playlists`, async ({ request }) => {
    const playlistData = await request.json() as any
    
    return HttpResponse.json({
      success: true,
      data: createMockPlaylist(playlistData)
    })
  }),

  // Search endpoints
  http.get(`${API_BASE}/search`, ({ request }) => {
    const url = new URL(request.url)
    const query = url.searchParams.get('q') || ''
    const type = url.searchParams.get('type') || 'all'
    
    const results = {
      music: type === 'all' || type === 'music' ? [createMockMusic()] : [],
      playlists: type === 'all' || type === 'playlists' ? [createMockPlaylist()] : [],
      users: type === 'all' || type === 'users' ? [createMockUser()] : []
    }
    
    return HttpResponse.json({
      success: true,
      data: results
    })
  }),

  // Social endpoints
  http.post(`${API_BASE}/social/follow/:userId`, () => {
    return HttpResponse.json({ success: true })
  }),

  http.delete(`${API_BASE}/social/follow/:userId`, () => {
    return HttpResponse.json({ success: true })
  }),

  http.post(`${API_BASE}/social/like`, () => {
    return HttpResponse.json({ success: true })
  }),

  http.delete(`${API_BASE}/social/like`, () => {
    return HttpResponse.json({ success: true })
  }),

  // Error simulation endpoints
  http.get(`${API_BASE}/test/error`, () => {
    return HttpResponse.json(
      { success: false, error: 'Test error' },
      { status: 500 }
    )
  }),

  http.get(`${API_BASE}/test/timeout`, () => {
    return new Promise(() => {
      // Never resolve to simulate timeout
    })
  })
]
