import { setupServer } from 'msw/node'
import { handlers } from './handlers'

// Setup MSW server for Node.js environment (used in tests)
export const server = setupServer(...handlers)

// Start server before all tests
export function setupMockServer() {
  // Start the server before all tests
  server.listen({
    onUnhandledRequest: 'warn'
  })
  
  console.log('🔧 Mock server started for testing')
}

// Reset handlers after each test
export function resetMockServer() {
  server.resetHandlers()
}

// Close server after all tests
export function closeMockServer() {
  server.close()
  console.log('🔧 Mock server closed')
}

// Export server for direct use in tests
export { server as mockServer }
