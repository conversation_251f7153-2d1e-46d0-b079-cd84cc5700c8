import { chromium, FullConfig } from '@playwright/test'

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup for E2E tests...')
  
  // Launch browser for setup
  const browser = await chromium.launch()
  const page = await browser.newPage()
  
  try {
    // Wait for the dev server to be ready
    console.log('⏳ Waiting for dev server to be ready...')
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' })
    console.log('✅ Dev server is ready')
    
    // You can add any global setup logic here
    // For example: seed test data, authenticate test users, etc.
    
  } catch (error) {
    console.error('❌ Global setup failed:', error)
    throw error
  } finally {
    await browser.close()
  }
  
  console.log('✅ Global setup completed')
}

export default globalSetup
