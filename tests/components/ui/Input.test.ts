import { describe, it, expect, vi } from 'vitest'
import { fireEvent } from '@testing-library/vue'
import { renderWithProviders } from '../../utils/test-utils'
import Input from '../../../components/ui/Input.vue'

describe('Input Component', () => {
  it('renders with default props', () => {
    const { getByRole } = renderWithProviders(Input, {
      props: {
        modelValue: '',
        placeholder: 'Enter text'
      }
    })

    const input = getByRole('textbox')
    expect(input).toBeInTheDocument()
    expect(input).toHaveAttribute('placeholder', 'Enter text')
  })

  it('renders different input types correctly', () => {
    const types = ['text', 'email', 'password', 'number', 'tel', 'url']
    
    types.forEach(type => {
      const { container } = renderWithProviders(Input, {
        props: { 
          modelValue: '',
          type 
        }
      })
      
      const input = container.querySelector('input')
      expect(input).toHaveAttribute('type', type)
    })
  })

  it('shows label when provided', () => {
    const { getByText } = renderWithProviders(Input, {
      props: {
        modelValue: '',
        label: 'Email Address'
      }
    })

    expect(getByText('Email Address')).toBeInTheDocument()
  })

  it('shows error state and message', () => {
    const { getByText, container } = renderWithProviders(Input, {
      props: {
        modelValue: '',
        error: 'This field is required'
      }
    })

    const input = container.querySelector('input')
    expect(input).toHaveClass('border-red-300')
    expect(getByText('This field is required')).toBeInTheDocument()
  })

  it('shows normal state without error', () => {
    const { container } = renderWithProviders(Input, {
      props: {
        modelValue: '<EMAIL>'
      }
    })

    const input = container.querySelector('input')
    expect(input).toHaveClass('border-gray-300')
  })

  it('is disabled when disabled prop is true', () => {
    const { container } = renderWithProviders(Input, {
      props: {
        modelValue: '',
        disabled: true
      }
    })

    const input = container.querySelector('input')
    expect(input).toBeDisabled()
    expect(input).toHaveClass('disabled:opacity-50')
  })

  it('emits update:modelValue when input changes', async () => {
    const updateHandler = vi.fn()
    const { container } = renderWithProviders(Input, {
      props: {
        modelValue: '',
        'onUpdate:modelValue': updateHandler
      }
    })

    const input = container.querySelector('input') as HTMLInputElement
    await fireEvent.update(input, 'new value')
    
    expect(updateHandler).toHaveBeenCalledWith('new value')
  })

  it('shows password toggle for password type', async () => {
    const { container } = renderWithProviders(Input, {
      props: {
        modelValue: 'password123',
        type: 'password'
      }
    })

    const toggleButton = container.querySelector('button[type="button"]')
    expect(toggleButton).toBeInTheDocument()

    const input = container.querySelector('input')
    expect(input).toHaveAttribute('type', 'password')

    // Click toggle to show password
    await fireEvent.click(toggleButton!)
    expect(input).toHaveAttribute('type', 'text')

    // Click again to hide password
    await fireEvent.click(toggleButton!)
    expect(input).toHaveAttribute('type', 'password')
  })

  it('shows clear button when clearable and has value', async () => {
    const updateHandler = vi.fn()
    const { container } = renderWithProviders(Input, {
      props: {
        modelValue: 'some text',
        clearable: true,
        'onUpdate:modelValue': updateHandler
      }
    })

    const clearButton = container.querySelector('button[type="button"]')
    expect(clearButton).toBeInTheDocument()

    await fireEvent.click(clearButton!)
    expect(updateHandler).toHaveBeenCalledWith('')
  })

  it('does not show clear button when no value', () => {
    const { container } = renderWithProviders(Input, {
      props: {
        modelValue: '',
        clearable: true
      }
    })

    const clearButton = container.querySelector('button[type="button"]')
    expect(clearButton).not.toBeInTheDocument()
  })

  it('renders with left icon', () => {
    const { container } = renderWithProviders(Input, {
      props: {
        modelValue: '',
        leftIcon: 'search'
      }
    })

    const iconContainer = container.querySelector('.absolute.inset-y-0.left-0')
    expect(iconContainer).toBeInTheDocument()
  })

  it('renders with right icon', () => {
    const { container } = renderWithProviders(Input, {
      props: {
        modelValue: '',
        rightIcon: 'calendar'
      }
    })

    const iconContainer = container.querySelector('.absolute.inset-y-0.right-0')
    expect(iconContainer).toBeInTheDocument()
  })

  it('supports different sizes', () => {
    const sizes = ['sm', 'md', 'lg'] as const

    sizes.forEach(size => {
      const { container } = renderWithProviders(Input, {
        props: {
          modelValue: '',
          size
        }
      })

      const input = container.querySelector('input')
      // Check for size-specific classes
      if (size === 'sm') {
        expect(input).toHaveClass('text-sm')
      } else if (size === 'lg') {
        expect(input).toHaveClass('text-base')
      } else {
        expect(input).toHaveClass('text-sm') // md is also text-sm
      }
    })
  })

  it('handles focus and blur events', async () => {
    const focusHandler = vi.fn()
    const blurHandler = vi.fn()
    
    const { container } = renderWithProviders(Input, {
      props: {
        modelValue: '',
        onFocus: focusHandler,
        onBlur: blurHandler
      }
    })

    const input = container.querySelector('input') as HTMLInputElement
    
    await fireEvent.focus(input)
    expect(focusHandler).toHaveBeenCalledTimes(1)
    
    await fireEvent.blur(input)
    expect(blurHandler).toHaveBeenCalledTimes(1)
  })
})
