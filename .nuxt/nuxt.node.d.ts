/// <reference types="@vueuse/nuxt" />
/// <reference types="@nuxtjs/color-mode" />
/// <reference types="@pinia/nuxt" />
/// <reference types="@nuxt/telemetry" />
/// <reference types="@nuxt/devtools" />
/// <reference types="@nuxtjs/tailwindcss" />
/// <reference path="types/modules.d.ts" />
/// <reference path="types/runtime-config.d.ts" />
/// <reference path="types/app.config.d.ts" />
/// <reference types="nuxt" />
/// <reference path="types/middleware.d.ts" />
/// <reference path="schema/nuxt.schema.d.ts" />

export {}
