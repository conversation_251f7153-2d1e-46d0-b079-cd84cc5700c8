// generated by the @nuxtjs/tailwindcss <https://github.com/nuxt-modules/tailwindcss> module at 2025/8/2 11:23:47
import "@nuxtjs/tailwindcss/config-ctx"
import configMerger from "@nuxtjs/tailwindcss/merger";

import cfg2 from "./../../tailwind.config.js";
const config = [
{"content":{"files":["/Users/<USER>/Desktop/musicdou-frontend/app/components/**/*.{vue,js,jsx,mjs,ts,tsx}","/Users/<USER>/Desktop/musicdou-frontend/app/components/global/**/*.{vue,js,jsx,mjs,ts,tsx}","/Users/<USER>/Desktop/musicdou-frontend/app/components/**/*.{vue,js,jsx,mjs,ts,tsx}","/Users/<USER>/Desktop/musicdou-frontend/app/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","/Users/<USER>/Desktop/musicdou-frontend/app/plugins/**/*.{js,ts,mjs}","/Users/<USER>/Desktop/musicdou-frontend/app/composables/**/*.{js,ts,mjs}","/Users/<USER>/Desktop/musicdou-frontend/app/utils/**/*.{js,ts,mjs}","/Users/<USER>/Desktop/musicdou-frontend/app/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","/Users/<USER>/Desktop/musicdou-frontend/app/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","/Users/<USER>/Desktop/musicdou-frontend/app/app.config.{js,ts,mjs}"]}},
{},
cfg2
].reduce((acc, curr) => configMerger(acc, curr), {});

const resolvedConfig = config;

export default resolvedConfig;