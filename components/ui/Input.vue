<template>
  <div class="w-full">
    <!-- Label -->
    <label
      v-if="label"
      :for="inputId"
      class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
    >
      {{ label }}
      <span v-if="required" class="text-red-500 ml-1">*</span>
    </label>

    <!-- Input Container -->
    <div class="relative">
      <!-- Left Icon -->
      <div
        v-if="leftIcon"
        class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
      >
        <Icon :name="leftIcon" :class="iconClasses" />
      </div>

      <!-- Input Element -->
      <input
        :id="inputId"
        ref="inputRef"
        :type="inputType"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :class="inputClasses"
        :autocomplete="autocomplete"
        :maxlength="maxlength"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
        @keydown="handleKeydown"
      />

      <!-- Right Icon / Password Toggle -->
      <div
        v-if="rightIcon || (type === 'password')"
        class="absolute inset-y-0 right-0 pr-3 flex items-center"
      >
        <button
          v-if="type === 'password'"
          type="button"
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none"
          @click="togglePasswordVisibility"
        >
          <Icon
            :name="showPassword ? 'EyeSlashIcon' : 'EyeIcon'"
            :class="iconClasses"
          />
        </button>
        <Icon
          v-else-if="rightIcon"
          :name="rightIcon"
          :class="iconClasses"
          class="pointer-events-none"
        />
      </div>

      <!-- Clear Button -->
      <button
        v-if="clearable && modelValue && !disabled && !readonly"
        type="button"
        class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        @click="clearInput"
      >
        <Icon name="XMarkIcon" :class="iconClasses" />
      </button>
    </div>

    <!-- Help Text -->
    <p
      v-if="helpText && !error"
      class="mt-2 text-sm text-gray-500 dark:text-gray-400"
    >
      {{ helpText }}
    </p>

    <!-- Error Message -->
    <p
      v-if="error"
      class="mt-2 text-sm text-red-600 dark:text-red-400"
    >
      {{ error }}
    </p>

    <!-- Character Count -->
    <p
      v-if="maxlength && showCount"
      class="mt-1 text-xs text-gray-500 dark:text-gray-400 text-right"
    >
      {{ modelValue?.length || 0 }}/{{ maxlength }}
    </p>
  </div>
</template>

<script setup lang="ts">
import { nextTick } from 'vue'

interface Props {
  modelValue?: string | number
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search'
  label?: string
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
  required?: boolean
  error?: string
  helpText?: string
  leftIcon?: string
  rightIcon?: string
  clearable?: boolean
  size?: 'sm' | 'md' | 'lg'
  autocomplete?: string
  maxlength?: number
  showCount?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'blur', event: FocusEvent): void
  (e: 'focus', event: FocusEvent): void
  (e: 'keydown', event: KeyboardEvent): void
  (e: 'clear'): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  disabled: false,
  readonly: false,
  required: false,
  clearable: false,
  size: 'md',
  showCount: false
})

const emit = defineEmits<Emits>()

// 生成唯一ID
const inputId = computed(() => `input-${Math.random().toString(36).substr(2, 9)}`)

// 密码显示状态
const showPassword = ref(false)

// 输入框引用
const inputRef = ref<HTMLInputElement>()

// 计算输入类型
const inputType = computed(() => {
  if (props.type === 'password') {
    return showPassword.value ? 'text' : 'password'
  }
  return props.type
})

// 事件处理
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('update:modelValue', target.value)
}

const handleBlur = (event: FocusEvent) => {
  emit('blur', event)
}

const handleFocus = (event: FocusEvent) => {
  emit('focus', event)
}

const handleKeydown = (event: KeyboardEvent) => {
  emit('keydown', event)
}

const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

const clearInput = () => {
  emit('update:modelValue', '')
  emit('clear')
  nextTick(() => {
    inputRef.value?.focus()
  })
}

// 样式计算
const inputClasses = computed(() => {
  const baseClasses = [
    'block',
    'w-full',
    'border',
    'rounded-lg',
    'transition-colors',
    'duration-200',
    'focus:outline-none',
    'focus:ring-2',
    'focus:ring-offset-2',
    'disabled:opacity-50',
    'disabled:cursor-not-allowed',
    'placeholder-gray-400',
    'dark:placeholder-gray-500'
  ]

  // 尺寸样式
  const sizeClasses = {
    sm: ['px-3', 'py-2', 'text-sm'],
    md: ['px-4', 'py-2.5', 'text-sm'],
    lg: ['px-4', 'py-3', 'text-base']
  }

  // 状态样式
  const stateClasses = props.error
    ? [
        'border-red-300',
        'dark:border-red-600',
        'bg-red-50',
        'dark:bg-red-900/20',
        'text-red-900',
        'dark:text-red-100',
        'focus:ring-red-500',
        'focus:border-red-500'
      ]
    : [
        'border-gray-300',
        'dark:border-gray-600',
        'bg-white',
        'dark:bg-gray-800',
        'text-gray-900',
        'dark:text-gray-100',
        'focus:ring-primary-500',
        'focus:border-primary-500'
      ]

  // 图标间距
  const paddingClasses = []
  if (props.leftIcon) {
    paddingClasses.push('pl-10')
  }
  if (props.rightIcon || props.type === 'password' || props.clearable) {
    paddingClasses.push('pr-10')
  }

  return [
    ...baseClasses,
    ...sizeClasses[props.size],
    ...stateClasses,
    ...paddingClasses
  ].join(' ')
})

const iconClasses = computed(() => {
  const sizeMap = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-5 h-5'
  }
  return `${sizeMap[props.size]} text-gray-400 dark:text-gray-500`
})

// 暴露方法
defineExpose({
  focus: () => inputRef.value?.focus(),
  blur: () => inputRef.value?.blur(),
  select: () => inputRef.value?.select()
})
</script>
